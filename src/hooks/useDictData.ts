import Dictionary_Api from "@/api/dictionary";

export const useDictData = () => {
  /**
   * 基础数据Id
   */
  const dictId = ref<string | undefined>(undefined);

  /**
   * 请求基础数据Id
   */
  async function requestBaseDictId(code: string) {
    const r = await Dictionary_Api.getDictByCode(code);
    if (r.Type === 200) {
      dictId.value = r.Data.Id;
    }

    return r;
  }

  /**
   * 请求字典基础数据列表
   *
   * 已经根据 code 获取 dictId，queryParams 中不需要设置 DictId
   *
   * @param code 字典Code
   * @param queryParams 查询参数
   *
   * @returns
   */
  async function requestDictDataList(code: string, queryParams: DictQueryParams) {
    if (!dictId.value) {
      const r = await requestBaseDictId(code);
      if (r.Type !== 200) {
        return {
          Type: r.Type,
          Content: r.Content,
          Data: {
            Total: 0,
            Rows: [],
          },
        };
      }
    }

    const params: DictQueryParams = JSON.parse(JSON.stringify(queryParams));
    const dictRule = params.FilterGroup.Rules?.find((item) => item.Field === "DictId");
    if (dictRule) {
      dictRule.Value = dictId.value ?? "";
    } else {
      params.FilterGroup.Rules ??= [];
      params.FilterGroup.Rules.push({
        Field: "DictId",
        Value: dictId.value ?? "",
        Operate: 3,
      });
    }
    const r = await Dictionary_Api.readDict(params);
    return r;
  }

  /**
   * 请求字典基础数据详情
   *
   * @param data 根据 dictId 和 id 查询
   * {
   *  dictId: 字典Id,
   *  id: 数据Id
   * }
   *
   * @param DictQueryParams 自定义查询条件
   *
   * @returns
   */
  async function requestDictDataDetail(data: { dictId: string; id: string } | DictQueryParams) {
    let params: DictQueryParams;
    if ("dictId" in data && "id" in data) {
      params = {
        FilterGroup: {
          Rules: [
            {
              Field: "DictId",
              Value: data.dictId,
              Operate: 3,
            },
            {
              Field: "Id",
              Value: data.id,
              Operate: 3,
            },
          ],
          Operate: 1,
        },
      };
    } else {
      params = data;
    }

    const r = await Dictionary_Api.readDict(params);
    return r;
  }

  /**
   * 发布数据
   * @param id 数据Id
   * @returns 是否成功
   */
  async function handlePublish(id: string): Promise<boolean> {
    const r = await Dictionary_Api.publishDict({ id: id });
    if (r.Type !== 200) {
      ElNotification.error(r.Content);
    } else {
      ElNotification.success("发布成功");
    }

    return r.Type === 200;
  }

  /**
   * 删除数据
   * @param id 数据Id
   * @returns 是否成功
   */
  async function handleDelete(id: string): Promise<boolean> {
    const promise = new Promise<boolean>(async (resolve) => {
      ElMessageBox.confirm("此操作将删除此条数据， 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const r = await Dictionary_Api.deleteDict({ id: id });
          if (r.Type !== 200) {
            ElNotification.error(r.Content);
            resolve(false);
          } else {
            ElNotification.success("删除成功");
            resolve(true);
          }
        })
        .catch(() => {
          resolve(false);
        });
    });

    return promise;
  }

  return {
    dictId,
    requestBaseDictId,
    requestDictDataList,
    requestDictDataDetail,
    handlePublish,
    handleDelete,
  };
};
