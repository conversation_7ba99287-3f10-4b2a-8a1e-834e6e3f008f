import request from "@/utils/request";
import {
  type PageQueryConsumableInputDTO,
  type MoItemPageData,
  type MoItemPageDataInputDTO,
  type PageQueryActionUnitParams,
  type ConsumableTypeInputDTO,
  type ConsumableInputDTO,
  type PushMoItemInputDTO,
  type InsertOrUpdateMoItemInputDTO,
  type ChargeItemPageDataInputDTO,
  type MoItemActionsInputDTO,
  type MoItemAction,
  type GetMoItemPackInputDTO,
  type MoItemPackItem,
  type MoItemPackInputDTO,
  type PageQueryContentInputDTO,
  type RecoveryMissionTypesInputDTO,
  type GetInstrumentInputDTO,
  type <PERSON>PhraseParams,
  type MoItemAliasInputDTO,
  type BaseProtocol,
  type OperateInstrumentInputDTO,
  type ActionUnitItem,
  type ProcessActionUnitInputDTO,
  type CreateRecoveryMissionTypeParams,
  type UpdateRecoveryMissionTypeParams,
} from "./types";

// --------------- dictionary_Dict ---------------
const Content_MoItem = "/content/api/MoItem";
const Content_ActionUnit = "/content/api/ActionUnit";
const Content_AcuPoint = "/content/api/AcuPoint";
const Content_Consumable = "/content/api/Consumables";
const Content_ChargeItem = "/content/api/ChargeItem";
const Content_RecoveryMission = "/content/api/RecoveryMission";
const Content_Instrument = "/content/api/Instrument";
const Content_Phrase = "/content/api/Phrase";
const Content_Protocol = "/content/api/Protocol";

const Content_Api = {
  getMoItemType(): Promise<ServerResult<MoItemTypeList[]>> {
    return request.get(`${Content_MoItem}/GetMoItemType`);
  },
  /**
   * 获取医嘱分类
   */
  getMoItemTags(): Promise<ServerResult<string[]>> {
    return request.get(`${Content_MoItem}/GetMoItemTags`);
  },
  pageQueryActionUnit(
    data: PageQueryActionUnitParams
  ): Promise<ServerResult<ListDataTotalCount<ActionUnit>>> {
    return request.post(`${Content_ActionUnit}/PageQueryActionUnit`, data);
  },
  /** 通过id获取动作详情 */
  queryActionUnitById(params: { id: string }): Promise<ServerResult<ActionUnitItem>> {
    return request.get(`${Content_ActionUnit}/QueryActionUnitById`, {
      params,
    });
  },
  /** 创建训练动作 */
  createActionUnit(data: ProcessActionUnitInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_ActionUnit}/CreateActionUnit`, data);
  },
  /** 更新训练动作 */
  updateActionUnit(data: ProcessActionUnitInputDTO): Promise<ServerResult<null>> {
    return request.put(`${Content_ActionUnit}/UpdateActionUnit`, data);
  },
  /** 批量推送训练动作 */
  pushActionItem(data: { Ids: string[]; OrgIds: string[] }): Promise<ServerResult<null>> {
    return request.post(`${Content_ActionUnit}/PushActionItem`, data);
  },
  /** 删除训练动作 */
  deleteActionUnit(data: { id: string }): Promise<ServerResult<null>> {
    return request.delete(`${Content_ActionUnit}/DeleteActionUnit`, {
      params: {
        Id: data.id,
      },
    });
  },
  /** 通过医嘱 id 获取穴位模板 */
  appGetAcuPointTemplates(params: {
    moItem: string;
  }): Promise<ServerResult<BaseAcuPointTemplate[]>> {
    return request.get(`${Content_AcuPoint}/APPGetAcuPointTemplates`, {
      params,
    });
  },
  /** 获取所有穴位 */
  getAcuPointPageData(params: {
    isEnable: boolean;
    page: number;
    pageSize: number;
  }): Promise<ServerResult<ListDataTotalCount<BaseAcuPoint>>> {
    return request.get(`${Content_AcuPoint}/GetAcuPointPageData`, {
      params,
    });
  },
  /**
   * 获取医嘱分页数据
   */
  getMoItemPageData(
    params: MoItemPageDataInputDTO
  ): Promise<ServerResult<ListDataTotalCount<MoItemPageData>>> {
    return request.get(`${Content_MoItem}/GetMoItemPageData`, {
      params,
    });
  },
  /** 获取医嘱简称 */
  getMoItemAliasName(data: MoItemAliasInputDTO): Promise<ServerResult<string[]>> {
    return request.post(`${Content_MoItem}/GetMoItemAliasName`, data);
  },

  getConsumablesTypePageData(): Promise<ServerResult<ConsumablesType[]>> {
    return request.get(`${Content_Consumable}/GetConsumablesTypePageData`);
  },
  getConsumablesPageData(
    params: PageQueryConsumableInputDTO
  ): Promise<ServerResult<ListDataTotalCount<BaseConsumables>>> {
    return request.get(`${Content_Consumable}/GetConsumablesPageData`, {
      params,
    });
  },
  insertOrUpdateConsumableType(data: ConsumableTypeInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_Consumable}/InsertOrUpdateConsumableType`, data);
  },
  insertOrUpdateConsumables(data: ConsumableInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_Consumable}/InsertOrUpdateConsumables`, data);
  },
  /**
   * 批量推送医嘱
   */
  pushMoItem(data: PushMoItemInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/PushMoItem`, data);
  },
  /** 插入或更新医嘱 */
  insertOrUpdateMoItem(data: InsertOrUpdateMoItemInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/InsertOrUpdateMoItem`, data);
  },
  /** 获取收费项目分页数据 */
  getChargeItemPageData(
    params: ChargeItemPageDataInputDTO
  ): Promise<ServerResult<ListDataTotalCount<BaseChargeItemType>>> {
    return request.get(`${Content_ChargeItem}/GetChargeItemPageData`, {
      params,
    });
  },
  /** 获取医嘱详情 */
  getMoItemByIds(data: { Ids: string[] }): Promise<ServerResult<BaseMoItemData[]>> {
    return request.post(`${Content_MoItem}/GetMoItemByIds`, data);
  },
  /** 获取康复训练 */
  getMoItemActions(data: MoItemActionsInputDTO): Promise<ServerResult<MoItemAction[]>> {
    return request.post(`${Content_MoItem}/GetMoItemActions`, data);
  },
  /** 获取服务包 */
  getMoItemPackList(
    params: GetMoItemPackInputDTO
  ): Promise<ServerResult<ListDataTotalCount<MoItemPackItem>>> {
    return request.get(`${Content_MoItem}/GetMoItemPackList`, {
      params,
    });
  },
  /** 删除服务包 */
  deleteMoItemPack(data: { Id: string }): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/DeleteMoItemPack`, data);
  },
  /** 批量操作服务包 */
  batchOperateMoItemPack(data: {
    Ids: string[];
    IsEnable?: boolean;
    IsDefaultPush?: boolean;
    DeptIds?: string[];
  }): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/BatchOperateMoItemPack`, data);
  },
  /** 批量推送服务包 */
  pushMoItemPack(data: { Ids: string[]; OrgIds: string[] }): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/PushMoItemPack`, data);
  },
  /** 修改服务包 */
  updateMoItemPack(data: MoItemPackInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/UpdateMoItemPack`, data);
  },
  /** 添加服务包 */
  addMoItemPack(data: MoItemPackInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_MoItem}/AddMoItemPack`, data);
  },
  /** 获取服务包详情 */
  getPackDetailByID(params: {
    packId: string;
    loadPackAmount: boolean;
  }): Promise<ServerResult<MoItemPackInputDTO>> {
    return request.get(`${Content_MoItem}/GetPackDetailByID`, {
      params,
    });
  },
  /** 获取动作里面的参数 */
  getInstrumentByIds(data: string[]): Promise<ServerResult<BaseInstrument[]>> {
    return request.post(`${Content_Instrument}/GetInstrumentByIds`, data);
  },
  /** 获取设备类型 */
  getInstrumentsPageData(
    params: GetInstrumentInputDTO
  ): Promise<ServerResult<ListDataTotalCount<BaseInstrument>>> {
    return request.get(`${Content_Instrument}/GetInstrumentsPageData`, {
      params,
    });
  },
  /** 删除设备 */
  deleteInstrument(data: { instrumentId: string }): Promise<ServerResult<null>> {
    return request.post(`${Content_Instrument}/DeleteInstrument?instrumentId=${data.instrumentId}`);
  },
  /** 发布设备 */
  publishInstrument(data: { instrumentId: string }): Promise<ServerResult<null>> {
    return request.post(
      `${Content_Instrument}/PublishInstrument?instrumentId=${data.instrumentId}`
    );
  },
  /** 添加或者编辑设备 */
  insertOrUpdateInstrument(data: OperateInstrumentInputDTO): Promise<ServerResult<null>> {
    return request.post(`${Content_Instrument}/InsertOrUpdateInstrument`, data);
  },
  /** 获取设备类型 */
  getInstrumentsWithCharge(): Promise<ServerResult<BaseInstrument[]>> {
    return request.get(`${Content_Instrument}/GetInstrumentsWithCharge`);
  },
  /** 获取设备类型 */
  getOriginalInstruments(): Promise<ServerResult<BaseInstrument[]>> {
    return request.get(`${Content_Instrument}/GetOriginalInstruments`);
  },

  // --------------- RecoveryMission ---------------

  /** 获取宣教数据 */
  pageQueryContent(
    data: PageQueryContentInputDTO
  ): Promise<ServerResult<ListDataTotal<BaseRecoveryMission>>> {
    return request.post(`${Content_RecoveryMission}/PageQueryContent`, data);
  },

  /** 获取所有宣教类型 */
  getAllRecoveryMissionTypes(
    params: RecoveryMissionTypesInputDTO
  ): Promise<ServerResult<RecoveryMissionType[]>> {
    return request.get(`${Content_RecoveryMission}/GetAllRecoveryMissionTypes`, {
      params,
    });
  },

  /** 新增宣教类型 */
  createRecoveryMissionType(data: CreateRecoveryMissionTypeParams): Promise<ServerResult<string>> {
    return request.post(`${Content_RecoveryMission}/CreateRecoveryMissionType`, data);
  },

  /** 更新宣教类型 */
  updateRecoveryMissionType(data: UpdateRecoveryMissionTypeParams): Promise<ServerResult<string>> {
    return request.put(`${Content_RecoveryMission}/UpdateRecoveryMissionType`, data);
  },

  /** 删除宣教类型 */
  deleteRecoveryMissionType(id: string): Promise<ServerResult<string>> {
    return request.delete(`${Content_RecoveryMission}/DeleteRecoveryMissionType`, {
      params: { id },
    });
  },

  /** 是否有启用的子节点 */
  recoveryMissionTypeHaveEnables(id: string): Promise<ServerResult<boolean>> {
    return request.get(`${Content_RecoveryMission}/RecoveryMissionTypeHaveEnables`, {
      params: { recoveryMissionTypeId: id },
    });
  },

  /** 删除宣教类型之前的操作检查 */
  recoveryMissionTypeIsOperate(id: string): Promise<ServerResult<boolean>> {
    return request.get(`${Content_RecoveryMission}/RecoveryMissionTypeIsOperate`, {
      params: { id },
    });
  },

  /** 获取宣教详情 */
  getRecoveryMissionById(contentId: string): Promise<ServerResult<BaseRecoveryMission>> {
    return request.get(`${Content_RecoveryMission}/GetRecoveryMissionById`, {
      params: { contentId },
    });
  },

  /** 新增宣教 */
  createRecoveryMissionContent(data: BaseRecoveryMission): Promise<ServerResult> {
    return request.post(`${Content_RecoveryMission}/CreateRecoveryMissionContent`, data);
  },

  /** 更新宣教 */
  updateRecoveryMissionContent(data: BaseRecoveryMission): Promise<ServerResult> {
    return request.put(`${Content_RecoveryMission}/UpdateRecoveryMissionContent`, data);
  },

  /** 删除宣教 */
  deleteRecoveryMissionById(id: string): Promise<ServerResult> {
    return request.delete(`${Content_RecoveryMission}/DeleteRecoveryMissionById`, {
      params: { id },
    });
  },

  /** 批量推送宣教 */
  pushRecoveryMissions(data: { Ids: string[]; OrgIds: string[] }): Promise<ServerResult> {
    return request.post(`${Content_RecoveryMission}/PushRecoveryMission`, data);
  },

  // --------------- Phrase ---------------

  /** 获取常用语列表 */
  getPhraseList(params: GetPhraseParams): Promise<ServerResult<ListDataTotalCount<Phrase>>> {
    return request.post(`${Content_Phrase}/Find`, params);
  },

  /** 更新常用语 */
  updatePhrases(data: Phrase[]): Promise<ServerResult> {
    return request.post(`${Content_Phrase}/Update`, data);
  },

  /** 新增常用语 */
  insertPhrases(data: Phrase[]): Promise<ServerResult> {
    return request.post(`${Content_Phrase}/Insert`, data);
  },

  /** 删除常用语 */
  deletePhrases(ids: string[]): Promise<ServerResult> {
    return request.post(`${Content_Phrase}/Delete`, ids);
  },

  /**
   * 通过协议类型获取所有协议内容
   * @param {*} type
   */
  getProtocolByType(type: number): Promise<ServerResult<BaseProtocol[]>> {
    return request.get(`${Content_Protocol}/GetProtocolByType`, {
      params: {
        type,
      },
    });
  },

  /**
   * 通过协议id查询协议最新内容
   * @param {*} type
   */
  getProtocolById(id: string): Promise<ServerResult<BaseProtocol>> {
    return request.get(`${Content_Protocol}/GetProtocolById`, {
      params: {
        id,
      },
    });
  },

  /**
   * 创建协议：仅平台运营端调用，可创建隐私 / 用户协议
   * @param {*} type
   */
  createProtocol(params: any): Promise<ServerResult<null>> {
    return request.post(`${Content_Protocol}/CreateProtocol`, params);
  },

  /**
   * 修改协议，仅平台运营端
   * @param {*} type
   */
  updateProtocol(params: any): Promise<ServerResult<null>> {
    return request.put(`${Content_Protocol}/UpdateProtocol`, params);
  },

  /**
   *  通过id查询协议历史
   * @param {*} id
   */
  getProtocolHistory(id: string): Promise<ServerResult<BaseProtocol[]>> {
    return request.get(`${Content_Protocol}/GetProtocolHistory`, {
      params: {
        id,
      },
    });
  },
};
export default Content_Api;
