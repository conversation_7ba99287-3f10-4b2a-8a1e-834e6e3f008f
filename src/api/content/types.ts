import { type EpPropMergeTypeWithNull } from "element-plus";
// ------------------------------ /content/api/moItem ------------------------------
export interface MoItemPageDataInputParams {
  page: number;
  pageSize: number;
  isEnable?: boolean;
  moItemUseScope?: number;
  moType?: number;
  actionUnit?: string;
  keywords?: string;
  isDefaultPush?: boolean;
  keywordMode?: number;
  moItemMethod?: number;
  tag?: string;
  catelog?: string;
}
export interface MoItemPageData {
  TreatInfos?: unknown[];
  Id: string;
  Remark: string;
  Name: string;
  AliasName: string;
  PYM: string;
  Code: string;
  ChargeMode: number;
  LogisticsDay: number;
  MoType: number;
  MaxAmount: number;
  MinAmount: number;
  ShowAmount?: number;
  OrganizationId?: string;
  OrganizationName?: string;
  MinDay: number;
  MaxDay: number;
  IsEnable: boolean;
  IsUseConsumable: boolean;
  Consumables: string[];
  IsDefaultPush: boolean;
  ConsumablesOutputDtos?: ConsumableInputDTO[];
  DefaultTrainingItem: string;
  DefaultActionUnit?: unknown;
  ChargeItems: ChargeItem[];
  MoItemAmount: number;
  CreatedTime: string;
  CreatorId: string;
  DeletedTime?: string;
  MoItemUseScope: number;
  BeReferencedNum: number;
  IsSpecialFreq: boolean;
  MoItemMethod: number;
  MoItemVideoUrl: string;
  Reference?: string;
  Explain?: string;
  Media: string[];
  Visibility: number;
  Scene: number;
  DoctorVisibility: boolean;
  TherapistVisibility: boolean;
  TherapistByDoctorVisibility: boolean;
  NurseVisibility: boolean;
  NurseByDoctorVisibility: boolean;
  ConsultScene: boolean;
  PatientManagerScene: boolean;
  HaveConsultServe: boolean;
  DefaultMoDay?: number;
  Manufacturer?: string;
  DeptIds?: string[];
  DeptDTO?: unknown[];
  SortTime?: string;
  Treats?: string[];
  Tags?: string[];
  Catelog?: string[];
  Type: number;
}
export interface AliasNameParams {
  DeptId?: string;
  FilterOrg: boolean;
  IsEnable: boolean;
  OrgId?: string;
}
export interface PageQueryActionUnitParams {
  PageSize: number;
  PageIndex: number;
  Enable?: EpPropMergeTypeWithNull<boolean>;
  Keyword?: string;
  UseScope?: number; // 0：公用的 1：私用
  Type?: EpPropMergeTypeWithNull<number>; // 0：训练动作 1：康复训练
  DysfunctionId?: EpPropMergeTypeWithNull<string>;
  PartId?: EpPropMergeTypeWithNull<string>;
  InstrumentId?: EpPropMergeTypeWithNull<string>;
  DiseaseDict?: EpPropMergeTypeWithNull<string>;
  OrganizationId?: EpPropMergeTypeWithNull<string>;
}
export interface MoItemPageDataInputDTO {
  page: number;
  pageSize: number;
  isEnable?: boolean;
  moItemUseScope?: number[];
  moType?: number;
  actionUnit?: string;
  keywords?: string;
  isDefaultPush?: boolean;
  keywordMode?: number;
  moItemMethod?: number;
  tag?: string;
  catelog?: string;
  isOrderBytBeReferenceNum?: boolean;
  currentOrganizationId?: string;
}
/** 查询耗材 */
export interface PageQueryConsumableInputDTO {
  page: number;
  pageSize: number;
  isEnable?: boolean;
  keywords?: string;
  type?: string | null;
}
/**
 * 耗材分类
 */
export interface ConsumableTypeInputDTO {
  Name: string;
  PYM: string;
  Code: string;
  IsEnable: boolean;
  CreateTime?: string;
  CreatorId?: string;
  ObjectId?: string;
}
/**
 * 耗材
 */
export interface ConsumableInputDTO {
  ObjectId?: string;
  Code: string;
  IsEnable: boolean;
  Name: string;
  PYM: string;
  PackRatio: number;
  PackUnit: string;
  Spec?: string;
  Type: string;
  Urls?: string[];
}
/**
 * 功能障碍类型
 */
export interface DysfunctionTypeInputDTO {
  Key: string;
  PinyinCode: string;
  Value: string;
  DictId: string | number;
  IsEnabled: boolean;
  Remark: string;
  OrgId?: string;
  IsPublish?: boolean;
  OrderNumber?: number;
  ParentId?: string;
  Id?: string;
}
export interface PushMoItemInputDTO {
  OrgIds: string[];
  Ids: string[];
}
export interface InsertOrUpdateMoItemInputDTO {
  Name: string;
  PYM: string;
  Code: string;
  MoType?: number;
  IsEnable: boolean;
  IsUseConsumable: boolean;
  MoItemUseScope: number;
  IsSpecialFreq: boolean | null;
  MoItemMethod: number;
  IsDefaultPush: boolean;
  Explain: string;
  MinDay: number;
  MaxDay: number;
  Consumables?: string[];
  TrainingItems?: string[];
  DefaultTrainingItem?: string;
  ChargeItems?: ChargeItem[];
  MoItemVideoUrl: string;
  Remark: string;
  MaxAmount: EpPropMergeTypeWithNull<number>;
  MinAmount: EpPropMergeTypeWithNull<number>;
  ShowAmount: EpPropMergeTypeWithNull<number>;
  ChargeMode?: number;
  LogisticsDay?: number;
  Media: string[];
  DefaultMoDay: number;
  Manufacturer?: number;
  AliasName: string;
  Tags: string[];
  Catelog: string[];
  Visibility: number;
  Scene: number;
  HaveConsultServe: boolean;
  Treats?: string[];
  ObjectId?: string;
}
export interface ChargeItemPageDataInputDTO {
  page: number;
  pageSize: number;
  isEnable: boolean;
  keywords: string;
  type: number;
}
export interface MoItemActionsInputDTO {
  DtoName?: EpPropMergeTypeWithNull<string>;
  MoItemId: string;
  PackId?: EpPropMergeTypeWithNull<string>;
  PageIndex: number;
  PageSize: number;
  Keyword?: string;
  DiseaseDict?: EpPropMergeTypeWithNull<string>;
}
export interface MoItemAction {
  ContentId: string;
  UseScope?: number;
  ApproveState?: unknown;
  ApproveReason?: unknown;
  Name: string;
  AliasName?: string;
  Reference?: string;
  Code?: string;
  PinyinCode?: string;
  CreatorId?: string;
  Creator?: string;
  CreatedTime?: string;
  Organization?: string;
  Enable: boolean;
  Type: number;
  Freq: number;
  Group: string;
  GroupCount?: number;
  EachGroupCount?: number;
  Duration?: number;
  DurationUnit?: number;
  Dysfunction?: string[];
  ActionUnitImgURL?: string;
  Part?: string[];
  Instrument?: string;
  InstrumentId?: string;
  IsIOT?: boolean;
  InstrumentParameter?: string;
  MediaType?: number;
  Media?: {
    Id: string;
    Url: string;
  }[];
  ActionInfo: string;
  Notes: string;
  ActionTime?: string;
  ActionStrength?: string;
  IntensiveTrainings?: unknown[];
  DiseaseDicts?: string[];
  UseNum?: number;
  Manufacturer?: number;
  MFType: number;
}
export interface GetMoItemPackInputDTO {
  packType: EpPropMergeTypeWithNull<number[]>;
  diseasesId?: EpPropMergeTypeWithNull<string>;
  deptId?: EpPropMergeTypeWithNull<string>;
  isDefaultPush?: EpPropMergeTypeWithNull<boolean>;
  isEnable: EpPropMergeTypeWithNull<boolean>;
  keywords: EpPropMergeTypeWithNull<string>;
  pageIndex: number;
  pageSize: number;
  loadPackAmount?: boolean;
  orgId?: EpPropMergeTypeWithNull<string>;
}
export interface MoItemPackItem {
  // 有些字段用不上 就不卸载里面了
  Id: string;
  Type: number;
  Sort: number;
  OrganizationId: string | null;
  Name: string;
  ExecutDay: number;
  TherapistRemark: string;
  DeptIds: string[];
  PackExplain: string;
  DiseasesIds: string[];
  DiseasesNames: string[];
  DiseasesName: string;
  Amount: number;
  MoItemNames: string;
  MaxDay: number;
  CreatorId: string;
  CreatedTime: string;
  OrganizationName: string | null;
  DeptNames: string | null;
  IsDefaultPush: boolean;
  Remark: string;
  DiagnosisName: string;
  MoItemPackDiagnosis: BaseDiagnosis[];
  IsEnable: boolean;
  MoItemGroupDetails: MoItemGroupItem[];
  Part: string[];
}
export interface MoItemPackInputDTO {
  DeptIds: string[];
  DiseasesIds?: string[]; // 这个字段需要前端自己组装生成
  ExecutDay: number;
  IsDefaultPush: boolean;
  IsEnable: boolean;
  MoItemGroupDetails: MoItemGroupItem[];
  MoItemPackDiagnosis?: BaseDiagnosis[]; // 这个字段需要前端自己组装生成
  Name: string;
  PackExplain: string;
  Remark: string;
  Sort: number;
  TherapistRemark: string;
  Type: number;
  PackId?: string; // 服务包编辑时候的 Id
  Id?: string;
  Part: string[];
}
export interface MoItemGroupItem {
  MoItemId: string;
  MoName: string;
  MoRemark: string;
  MoDay: number;
  Freq: number;
  TotalCount: number;
  Price: number;
  FreqDay: number;
  IsSpecialFreq: boolean;
  MoItemMethod: number;
  MoItemUseScope: number;
  LogisticsDay: number;
  PackAcupoints?: AcuPointInfo[];
  PackActions?: MoItemAction[];
  PackScales?: PackScaleItem[];
  ChargeMode?: number;
  Part?: number;
  ChargeItem?: ChargeItem;
  TotalPrice?: number;
  MoMonth?: number;
  Consumables?: string[];
  RecoveryMissionRelations?: string[];
  Manufacturer?: number;
  MaxDay?: number;
  MinDay?: number;
  DefaultMoDay?: number;
}
export interface PackScaleItem {
  ScaleId: string;
  Name: string;
  ScaleRemarks: string;
  RxDetailTemplateId?: string; // 编辑的时候才需要传递（常用方案）
}
export interface PageQueryContentInputDTO {
  Enable?: boolean;
  IsDefaultPush?: boolean;
  KeyWord?: string;
  OrganizationId?: string;
  PageIndex: number;
  PageSize: number;
  RecoveryMissionType?: string;
  DeptId?: string;
  Diseases?: string[];
  IsRecommend?: boolean;
}
export interface RecoveryMissionTypesInputDTO {
  organizationId?: string;
  isShowOperation: boolean;
  isShowDisease: boolean;
  isShowRegime: boolean;
}

export interface PageBaseChargeItemType extends Omit<BaseChargeItemType, "Id"> {
  Id?: string;
  ChargeItemId: string;
}
export interface GetInstrumentInputDTO {
  page: number;
  pageSize: number;
  isEnable: EpPropMergeTypeWithNull<boolean>;
  keywords?: string;
  isIOTDevice?: boolean;
}
export interface GetPhraseParams {
  Source?: number;
  Class?: number;
  OrganizationId?: string;
  Keyword?: string;
  Enable?: boolean;
  pageIndex: number;
  pageSize: number;
}
export interface MoItemAliasInputDTO {
  DeptId: EpPropMergeTypeWithNull<string>;
  OrgId: EpPropMergeTypeWithNull<string>;
  FilterOrg: boolean;
  IsEnable: boolean;
}

/**
 * 协议
 */
export interface BaseProtocol {
  ContentId: string;
  ReleasePosition: number;
  ProtocolType: number;
  Text: string;
  Version: number;
  UpdatedTime: string;
  CreatedTime: string;
  CreatorId: string;
  CreatorName: string;
}
export interface OperateInstrumentInputDTO {
  InstrumentId?: string;
  Name: string;
  PYM: string;
  Code: string;
  DeviceType: number;
  IsEnable: boolean;
  IsIOTDevice: boolean;
  DeviceFactory: string;
  Parameters: string;
}
export interface ActionUnitItem {
  ContentId?: string;
  UseScope?: number;
  ApproveState?: any;
  ApproveReason?: any;
  Name?: string;
  AliasName?: any;
  Reference?: any;
  Code?: string;
  PinyinCode?: string;
  CreatorId?: string;
  Creator?: string;
  CreatedTime?: string;
  Organization?: string;
  Enable?: boolean;
  Type?: number;
  Freq?: number;
  GroupCount?: any;
  EachGroupCount?: any;
  Duration?: any;
  DurationUnit?: any;
  Dysfunction?: string[];
  ActionUnitImgURL?: string;
  Part?: any[];
  Instrument?: string;
  IsIOT?: boolean;
  InstrumentParameter?: any;
  MediaType?: number;
  Media?: {
    Id: string;
    Url: string;
  }[];
  FollowVideo?: any[];
  ActionInfo?: string;
  Notes?: string;
  ActionTime?: any;
  ActionStrength?: any;
  IntensiveTrainings?: any[];
  DiseaseDicts?: any[];
  UseNum?: number;
  Manufacturer?: number;
  MFType?: number;
  Id?: string;
}
export interface ProcessActionUnitInputDTO {
  Name: string;
  Code: string;
  PinyinCode: string;
  Type: number;
  Freq: number;
  GroupCount: EpPropMergeTypeWithNull<number>;
  EachGroupCount: EpPropMergeTypeWithNull<number>;
  Duration: EpPropMergeTypeWithNull<number>;
  Enable: boolean;
  DurationUnit: EpPropMergeTypeWithNull<number>;
  Dysfunction: string[];
  FollowVideo: string[];
  DiseaseDicts: string[];
  Part: string[];
  Instrument: EpPropMergeTypeWithNull<string>;
  MediaType: number;
  Media: { url: string }[];
  ActionUnitImgURL: string;
  ActionInfo: string;
  Notes: string;
  IntensiveTrainings: unknown[];
  UseScope: number;
  MFType: EpPropMergeTypeWithNull<number>;
  Id?: string;
}

export interface CreateRecoveryMissionTypeParams {
  Name: string;
  Code: string;
  Enable: boolean;
  OrganizationId?: string;
  ParentId?: string;
}

export interface UpdateRecoveryMissionTypeParams {
  Code: string;
  Enable: boolean;
  Id: string;
  Name: string;
  OrganizationId?: string;
  ParentId?: string;
}
