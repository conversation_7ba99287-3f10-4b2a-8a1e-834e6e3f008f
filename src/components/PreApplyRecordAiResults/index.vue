<template>
  <div>test</div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { LatestPreVisitAiAnalysisInputDTO } from "@/api/consult/types";

const onGetPreApplyRecordAiResults = async () => {
  const res = await Consult_Api.getLatestPreVisitAiAnalysis(props.params);
  if (res.Type === 200 && res.Data) {
    handleProcessData(res.Data);
  }
};

const handleProcessData = (data: string) => {
  try {
    const jsonData = JSON.parse(data);
    console.log(jsonData);
  } catch (error) {
    console.error(error);
  }
};

interface Props {
  params: LatestPreVisitAiAnalysisInputDTO;
}
const props = defineProps<Props>();
watch(
  () => props.params,
  (newVal) => {
    console.log(newVal);
    onGetPreApplyRecordAiResults();
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
