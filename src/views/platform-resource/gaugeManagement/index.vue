<template>
  <el-container class="w-full h-full">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px"
        :data="treeData"
        :props="defaultTreeProps"
        node-key="value"
        :current-node-key="queryParams.Type"
        highlight-current
        default-expand-all
        @node-click="onTreeClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <!-- 顶部筛选条件 -->
          <TBSearchContainer>
            <template #left>
              <el-form label-position="right" :model="queryParams" :inline="true">
                <el-form-item label="是否启用" prop="IsEnble">
                  <KSelect
                    v-model="queryParams.IsEnble"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="适用病种" prop="DiseaId">
                  <KSelect
                    v-model="queryParams.DiseaId"
                    :data="diseaseList"
                    :props="{ label: 'Key', value: 'Id' }"
                    :loading="diseaseLoading"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="康复分类" prop="DictType">
                  <KSelect
                    v-model="queryParams.DictType"
                    :data="recoveryTypeList"
                    :props="{ label: 'Key', value: 'Key' }"
                    :loading="recoveryTypeLoading"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="是否默认推送" prop="IsDefaultPush">
                  <KSelect
                    v-model="queryParams.IsDefaultPush"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="关键字" prop="Keyword">
                  <el-input
                    v-model="queryParams.Keyword"
                    clearable
                    placeholder="输入关键字筛选"
                    @keyup.enter="onSearch"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="onSearch">搜索</el-button>
              <el-button type="primary" @click="onBatchPushGauge">批量推送</el-button>
              <el-button type="primary" @click="onAddGauge">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 量表列表 -->
        <template #table>
          <el-table
            :ref="kTableRef"
            :data="pageData"
            highlight-current-row
            border
            :height="tableFluidHeight"
            @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="Code" label="编码" align="center" width="150" />
            <el-table-column prop="Name" label="量表名称" align="center" width="200" />
            <el-table-column prop="Types" label="康复分类" align="center" width="150" />
            <el-table-column prop="Remark" label="量表说明/介绍" align="center">
              <template #default="scope">
                <span class="line-clamp-2">{{ scope.row.Remark }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="IsEnbleText" label="是否启用" align="center" width="100" />
            <el-table-column prop="CreatedTime" label="创建时间" align="center" width="200" />
            <el-table-column prop="IsDefaultPushText" label="是否推送" align="center" width="100" />
            <el-table-column fixed="right" label="操作" width="180" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="onGaugeDetail(scope.row)">查看</el-button>
                <el-button link type="primary" @click="onEditGauge(scope.row)">编辑</el-button>
                <el-button link type="primary" @click="onDeleteGauge(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="requestGaugeData"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑/查看量表 -->
  <el-dialog
    v-model="showGaugeDialog.isShow"
    :title="showGaugeDialog.title"
    width="800"
    destroy-on-close
    @close="showGaugeDialog.isShow = false"
  >
    <GaugeForm
      :gauge="showGaugeDialog.gauge"
      :recovery-types="recoveryTypeOriginList"
      :diseases="diseaseOriginList"
      :disabled="showGaugeDialog.isDisabled"
      @cancel="showGaugeDialog.isShow = false"
      @submit="onConfirmSubmitGauge"
    />
  </el-dialog>
  <!-- 选择机构 -->
  <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
    <HospitalTransfer
      :loading="orgDialogLoading"
      @cancel="showOrgDialog = false"
      @submit="onConfirmOrganizations"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import Dictionary_Api from "@/api/dictionary";
import Training_Api from "@/api/training";
import { GetEvaluateGaugeInputDTO } from "@/api/training/types";
import useOrgDialog from "@/hooks/useOrgDialog";
import { useTableConfig } from "@/hooks/useTableConfig";
import GaugeForm from "./components/GaugeForm.vue";

const kEnableDebug = false;
defineOptions({
  name: "GaugeManagement",
  inheritAttrs: false,
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
} = useTableConfig<TableData>();

const { showOrgDialog, orgDialogLoading } = useOrgDialog();

interface TableData extends BaseGauge {
  IsEnbleText?: string;
  IsDefaultPushText?: string;
}

// 查询条件
const queryParams = reactive<GetEvaluateGaugeInputDTO>({
  PageIndex: 1,
  PageSize: 20,
});

// 点击搜索
async function onSearch() {
  kEnableDebug && console.log("点击搜索", queryParams);
  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 确认选择机构
async function onConfirmOrganizations(organizationIds: string[]) {
  kEnableDebug && console.log("选择机构", organizationIds);

  // 推送
  orgDialogLoading.value = true;
  const gaugeIds = selectedTableIds.value ?? [];
  const r = await Training_Api.pushGauge({ gaugeIds, organizationIds });
  orgDialogLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showOrgDialog.value = false;
  ElNotification.success("推送成功");

  // 清空选项
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
}

// 点击批量推送
function onBatchPushGauge() {
  kEnableDebug && console.log("批量推送");
  if (!selectedTableIds.value?.length) {
    ElMessage.warning("请选择需要推送的表单");
    return;
  }

  showOrgDialog.value = true;
}

// 查看/添加/编辑量表弹窗
const showGaugeDialog = reactive({
  isShow: false,
  title: "",
  isDisabled: false,
  gauge: {} as GaugeDetail, // 新增/编辑量表详情
});

// 点击添加量表
function onAddGauge() {
  kEnableDebug && console.log("添加量表");
  if (queryParams.Type === undefined) {
    ElMessage.warning("请先在左侧选择表单类型再添加");
    return;
  }

  showGaugeDialog.gauge = {
    Type: queryParams.Type,
    IsEnble: true,
    GaugeProblems: [],
  };
  showGaugeDialog.title = "添加量表";
  showGaugeDialog.isDisabled = false;
  showGaugeDialog.isShow = true;
}

// 点击查看量表
async function onGaugeDetail(item: BaseGauge) {
  kEnableDebug && console.log("查看量表", item);
  if (!item.Id) {
    ElMessage.error("量表id为空");
    return;
  }

  // 获取量表详情
  tableLoading.value = true;
  const r = await Training_Api.getGaugeById(item.Id);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showGaugeDialog.gauge = r.Data;
  showGaugeDialog.title = "查看量表";
  showGaugeDialog.isDisabled = true;
  showGaugeDialog.isShow = true;
}

// 点击编辑量表
async function onEditGauge(item: BaseGauge) {
  kEnableDebug && console.log("编辑量表", item);
  if (!item.Id) {
    ElMessage.error("量表id为空");
    return;
  }

  // 获取量表详情
  tableLoading.value = true;
  const r = await Training_Api.getGaugeById(item.Id);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showGaugeDialog.gauge = r.Data;
  showGaugeDialog.title = "编辑量表";
  showGaugeDialog.isDisabled = false;
  showGaugeDialog.isShow = true;
}

// 确认提交量表
async function onConfirmSubmitGauge() {
  showGaugeDialog.isShow = false;
  const r1 = await requestGaugeData();
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
    return;
  }

  if (showGaugeDialog.gauge?.Id) {
    ElNotification.success("编辑成功");
  } else {
    ElNotification.success("添加成功");
  }
}

// 点击删除量表
function onDeleteGauge(item: BaseGauge) {
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const gaugeId = item.Id;
    if (!gaugeId) {
      ElMessage.error("量表id为空");
      return;
    }

    handleDeleteGauge(gaugeId);
  });
}

// 删除量表+刷新列表
async function handleDeleteGauge(gaugeId: string) {
  // 删除
  tableLoading.value = true;
  const r = await Training_Api.deleteEvaluateGauge(gaugeId);
  if (r.Type !== 200) {
    tableLoading.value = false;
    ElMessage.error(r.Content);
    return;
  }

  // 刷新列表
  const r1 = await requestGaugeData();
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
    return;
  }

  ElNotification.success("删除成功");
}

// 请求量表列表
async function requestGaugeData() {
  tableLoading.value = true;
  const r = await Training_Api.getEvaluateGaugePage(queryParams);
  tableLoading.value = false;
  if (r.Type === 200) {
    pageData.value = r.Data.Rows.map((item) => {
      const data = {
        ...item,
        IsEnbleText: item.IsEnble ? "是" : "否",
        IsDefaultPushText: item.IsDefaultPush ? "是" : "否",
      };
      try {
        data.Types = JSON.parse(item.Types!).join("、");
      } catch (error) {
        data.Types = item.Types;
      }
      return data;
    });
    total.value = r.Data.Total;
  }

  return r;
}

interface Tree {
  // 展示数据
  label: string;
  // 实际网络请求需要的值
  value?: number;
  children?: Tree[];
}
// 左侧列表数据
const treeData = reactive<Tree[]>([
  {
    label: "全部",
    children: [
      {
        label: "评定量表",
        value: 0,
      },
      {
        label: "随访表单",
        value: 1,
      },
    ],
  },
]);
const defaultTreeProps = reactive({
  children: "children",
  label: "label",
});

// 树点击事件
async function onTreeClick(data: Tree) {
  kEnableDebug && console.debug("树点击事件", data);
  queryParams.Type = data.value;
  queryParams.PageIndex = 1;

  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 康复分类列表
const recoveryTypeOriginList: ReadDict[] = [];
const recoveryTypeList = reactive<ReadDict[]>([]);
const recoveryTypeLoading = ref<boolean>(false);
// 请求康复分类数据
async function requestRecoveryClassifyData() {
  recoveryTypeLoading.value = true;
  const r = await Dictionary_Api.getDictByCode("RecoveryClassify");
  recoveryTypeLoading.value = false;
  if (r.Type !== 200) return r;
  const id = r.Data.Id;
  if (!id) {
    return {
      Type: 500,
      Content: "分类id为空",
    };
  }

  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 999,
      SortConditions: [
        { SortField: "CustomSort", ListSortDirection: 1 },
        { SortField: "CreatedTime", ListSortDirection: 1 },
      ],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: id, Operate: 3 },
        { Field: "IsEnabled", Value: true, Operate: 3 },
        { Field: "IsPublish", Value: true, Operate: 3 },
      ],
      Operate: 1,
    },
  };
  const r1 = await Dictionary_Api.readDict(params);
  if (r1.Type === 200) {
    recoveryTypeOriginList.push(...(r1.Data.Rows ?? []));
    recoveryTypeList.push(...recoveryTypeOriginList);
  }

  return r1;
}

// 疾病列表
const diseaseOriginList: ReadDict[] = [];
const diseaseList = reactive<ReadDict[]>([]);
const diseaseLoading = ref<boolean>(false);
// 请求疾病列表
async function requestDiseaseData() {
  diseaseLoading.value = true;
  const r = await Dictionary_Api.getDict({ code: "DiseaseDict" });
  diseaseLoading.value = false;
  if (r.Type === 200) {
    diseaseOriginList.push(...(r.Data ?? []));
    diseaseList.push(...diseaseOriginList);
  }
  return r;
}

onMounted(async () => {
  const rs = await Promise.all([
    requestRecoveryClassifyData(),
    requestDiseaseData(),
    requestGaugeData(),
  ]);
  const fail = rs.find((r) => r.Type !== 200);
  if (fail) {
    ElMessage.error(fail.Content);
  }
});
</script>

<style lang="scss" scoped></style>
