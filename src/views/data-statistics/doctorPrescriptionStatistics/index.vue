<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  :shortcuts="datePickerShortcuts"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="医院">
                <HospitalSelect v-model="queryParams.OrgIds" :scopeable="true" />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect v-model="queryParams.DeptId" :org-id="queryParams.OrgIds" />
              </el-form-item>
              <el-form-item label="是否展示无数据医生">
                <el-select
                  v-model="queryParams.IncludeNoDataDoctor"
                  style="width: 80px"
                  placeholder="请选择"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              v-hasNoPermission="['externalSeller']"
              type="primary"
              :loading="exportLoading"
              :disabled="!pageData.length"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="DoctorName" label="姓名" show-overflow-tooltip align="center" />
          <el-table-column
            width="140"
            prop="OrgName"
            label="医院"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column prop="DeptName" label="科室" show-overflow-tooltip align="center" />
          <el-table-column prop="AssistantName" label="医助" show-overflow-tooltip align="center" />
          <el-table-column prop="RoleName" label="角色" show-overflow-tooltip align="center" />
          <el-table-column
            prop="FreePrescriptionCount"
            label="下达方案数（免费）"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="FreeExecPrescriptionCount"
            label="已执行方案数（免费）"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="FreePrescriptionExecRate"
            label="方案执行率（免费）"
            show-overflow-tooltip
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.FreePrescriptionExecRate > 0">
                {{ scope.row.FreePrescriptionExecRate }}%
              </span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="PaidPrescriptionCount"
            label="下达方案数（付费）"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="PaidExecPrescriptionCount"
            label="已执行方案数（付费）"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="PaidPrescriptionExecRate"
            label="方案执行率（付费）"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="scope.row.PaidPrescriptionExecRate > 0">
                {{ scope.row.PaidPrescriptionExecRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="RefundPartPrescriptionCount"
            label="部分退款方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="RefundWholePrescriptionCount"
            label="全额退款方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="PrescriptionRefundRate"
            label="退款率"
            show-overflow-tooltip
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.PrescriptionRefundRate > 0">
                {{ scope.row.PrescriptionRefundRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="ExecAmount"
            label="已执行金额"
            show-overflow-tooltip
            align="center"
          >
            <template #default="scope">
              <span class="text-green-500">{{ scope.row.ExecAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="RefundAmount"
            label="退款金额"
            show-overflow-tooltip
            align="center"
          >
            <template #default="scope">
              <span class="text-red-500">{{ scope.row.RefundAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="CiReCount"
            label="磁疗方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="HuXiCount"
            label="呼吸方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="ChaoShenCount"
            label="超声方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="GeWuCount"
            label="灸法方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="MaiZhenCount"
            label="埋针方案数"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="YunDongCount"
            label="运动方案数"
            show-overflow-tooltip
            align="center"
          />
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  DoctorPrescriptionStatisticsInputDTO,
  DoctorPrescriptionStatisticsItem,
  ExportTaskRedashDTO,
} from "@/api/report/types";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";
import { useUserStore } from "@/store";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();
const userStore = useUserStore();

defineOptions({
  name: "DoctorPrescriptionStatistics",
});

const queryParams = ref<DoctorPrescriptionStatisticsInputDTO>({
  BeginTimeDt: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTimeDt: dayjs().format("YYYY-MM-DD 23:59:59"),
  OrgIds: null,
  DeptId: null,
  Keyword: "",
  IncludeNoDataDoctor: "0",
  PageIndex: 1,
  PageSize: 20,
  LoginUserId: userStore.userInfo.Id,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);
const queryResultId = ref<number>(0);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, exportLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<DoctorPrescriptionStatisticsItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const redashParams = convertToRedashParams<DoctorPrescriptionStatisticsInputDTO>(
    queryParams.value,
    "Report_DoctorPrescriptionStatistics"
  );
  const res = await Report_Api.getRedashList<DoctorPrescriptionStatisticsItem>(redashParams);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    queryResultId.value = res.Data.QueryResultId;
  }
  tableLoading.value = false;
};

const handleSortChange = (column: any, prop: string, order: string) => {
  console.log(column, prop, order);
};

const handleExportExcel = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  const exportParams = convertToRedashParams<DoctorPrescriptionStatisticsInputDTO>(
    copyData,
    "Report_DoctorPrescriptionStatistics"
  );
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `医生开方统计-${Date.now()}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId.value,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.value.PageIndex,
    PageSize: queryParams.value.PageSize,
    QueryName: "Report_DoctorPrescriptionStatistics",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.BeginTimeDt = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTimeDt = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
