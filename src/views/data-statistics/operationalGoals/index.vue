<template>
  <div class="operational-goals">
    <!-- 左侧添加按钮区域 -->
    <div v-hasPermission="['superAdmin']" class="left-section">
      <div class="goal-card add-card" @click="handleAddGoal">
        <el-icon><Plus /></el-icon>
        <span>制定目标</span>
      </div>
    </div>
    <!-- 右侧目标展示区域 -->
    <div class="right-section">
      <!-- 目标卡片展示区 -->
      <div class="carousel-container" :loading="loading">
        <el-carousel
          ref="carousel"
          :autoplay="false"
          :initial-index="currentPage"
          :indicator-position="'none'"
          height="100%"
          trigger="click"
          arrow="always"
          @change="handleCarouselChange"
        >
          <el-carousel-item v-for="(group, groupIndex) in groupedGoals" :key="groupIndex">
            <div class="goals-grid">
              <div v-for="(goal, index) in group" :key="index" class="goal-card">
                <div class="card-header">
                  {{ goal.Month }}
                  <el-button
                    v-hasPermission="['superAdmin']"
                    class="edit-button"
                    type="primary"
                    :icon="Edit"
                    circle
                    size="small"
                    @click.stop="handleEditGoal(goal)"
                  />
                </div>
                <div class="goal-item" @click="handleGoalDetails(goal.Month, 'amount')">
                  <div class="goal-label">目标金额（元）：¥{{ goal.TargetAmount }}</div>
                  <LineProgress
                    text-inside
                    :stroke-width="20"
                    :format="(e: number) => handleFormatContent(e, goal, 'Amount')"
                    :percentage="
                      goal.TargetAmountCompletionRate > 100 ? 100 : goal.TargetAmountCompletionRate
                    "
                    :color="getProgressColor(goal.TargetAmountCompletionRate)"
                    text-align="left"
                    :middle-progress="{
                      percentage: currentDateProgress,
                      color: '#FFC7BA',
                    }"
                  />
                </div>
                <div class="goal-item" @click="handleGoalDetails(goal.Month, 'selfReliance')">
                  <div class="goal-label">自运行目标（单）：{{ goal.TargetSelfRelianceCount }}</div>
                  <LineProgress
                    text-inside
                    :stroke-width="20"
                    :format="(e: number) => handleFormatContent(e, goal, 'SelfRelianceCount')"
                    :percentage="
                      Number(goal.SelfRelianceCompletionRate) > 100
                        ? 100
                        : Number(goal.SelfRelianceCompletionRate)
                    "
                    :color="getProgressColor(Number(goal.SelfRelianceCompletionRate))"
                    text-align="left"
                    :middle-progress="{
                      percentage: currentDateProgress,
                      color: '#FFC7BA',
                    }"
                  />
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible.operationalGoals"
      title="制定目标"
      width="800px"
      destroy-on-close
    >
      <AddGoals ref="addGoalsRefs" :month="goalMonth" :is-edit="isEdit" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.operationalGoals = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleAddGoalSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer
      v-model="dialogVisible.goalsDetails"
      direction="rtl"
      :title="drawerTitle"
      size="100%"
    >
      <GoalDetails :goal-month="goalMonth" :goal-type="goalType" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Plus, Edit } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import Report_Api from "@/api/report";
import { BusinessGoalCompletionSummary } from "@/api/report/types";
import AddGoals from "./components/AddGoals.vue";
import Passport_Api from "@/api/passport";
import { getDecimalNum } from "@/utils";
import GoalDetails from "./components/GoalDetails.vue";
import LineProgress from "./components/LineProgress.vue";

interface PageBusinessGoalCompletionSummary
  extends Omit<
    BusinessGoalCompletionSummary,
    | "Amount"
    | "SelfRelianceCount"
    | "TargetAmount"
    | "TargetAmountCompletionRate"
    | "TargetSelfRelianceCount"
    | "SelfRelianceCompletionRate"
  > {
  Amount: number;
  SelfRelianceCount: number;
  TargetAmount: number;
  TargetAmountCompletionRate: number;
  TargetSelfRelianceCount: number;
  SelfRelianceCompletionRate: number;
}

interface QueryParams {
  queryName: string;
  parameters: {
    BeginTimeDt: string;
    EndTimeDt: string;
  };
  maxAge: number;
  JobWaitingMs: number;
  pageIndex: number;
  pageSize: number;
}

defineOptions({
  name: "OperationalGoals",
  inheritAttrs: false,
});

const currentPage = ref<number>(0);
const goalsList = ref<PageBusinessGoalCompletionSummary[]>([]);
const dialogVisible = ref<Record<string, boolean>>({
  operationalGoals: false,
  goalsDetails: false,
});
const goalMonth = ref<string>("");
const loading = ref<boolean>(false);
const goalType = ref<string>("");
const carousel = ref();
const addGoalsRefs = ref<InstanceType<typeof AddGoals> | null>(null);
const dialogConfirmLoading = ref<boolean>(false);
const isEdit = ref<boolean>(false);

const queryParams = ref<QueryParams>({
  queryName: "BusinessGoalCompletionSummary_CH",
  parameters: {
    BeginTimeDt: "2020-01",
    EndTimeDt: dayjs(new Date()).format("YYYY-MM-DD"),
  },
  maxAge: 0,
  JobWaitingMs: 30000,
  pageIndex: 1,
  pageSize: 9999,
});

const groupedGoals = computed(() => {
  const itemsPerPage = 9;
  const result = [];
  for (let i = 0; i < goalsList.value.length; i += itemsPerPage) {
    result.push(goalsList.value.slice(i, i + itemsPerPage));
  }
  return result;
});
const drawerTitle = computed(() => {
  return goalType.value === "amount"
    ? `${goalMonth.value}付费金额目标详情`
    : `${goalMonth.value}自运行目标详情`;
});
const handleFormatContent = (
  percentage: number,
  goal: PageBusinessGoalCompletionSummary,
  type: "Amount" | "SelfRelianceCount"
) => {
  return type === "Amount"
    ? `¥${goal.Amount} （${goal.TargetAmountCompletionRate}%）`
    : `${goal.SelfRelianceCount}（${goal.SelfRelianceCompletionRate}%）`;
};

/** 获取当前日期进度 */
const getCurrentDateProgress = (): number => {
  try {
    // 获取当前时间
    const currentTime = dayjs();

    // 获取当月开始时间（月初00:00:00）
    const monthStart = dayjs().startOf("month");

    // 获取当月结束时间（月末23:59:59）
    const monthEnd = dayjs().endOf("month");

    // 计算当前时间距离月初的时间差（毫秒）
    const currentDiff = currentTime.valueOf() - monthStart.valueOf();

    // 计算当月总时间差（毫秒）
    const totalDiff = monthEnd.valueOf() - monthStart.valueOf();

    // 使用getDecimalNum进行精确的百分比计算，保留2位小数
    const progress = getDecimalNum(currentDiff).div(getDecimalNum(totalDiff)).times(100).toNumber();

    // 确保返回值在0-100范围内
    return Math.max(0, Math.min(100, Number(progress.toFixed(2))));
  } catch (error) {
    // 如果计算过程中出现错误，返回0
    console.error("计算当前日期进度时出错:", error);
    return 0;
  }
};
const currentDateProgress = ref<number>(getCurrentDateProgress());

const refreshGoalsList = () => {
  queryParams.value.pageIndex = 1;
  getGoalsList();
};

const getGoalsList = async () => {
  try {
    loading.value = true;
    const res = await Report_Api.getRedashList<BusinessGoalCompletionSummary>(queryParams.value);
    if (res.Type === 200) {
      const newData = res.Data.Data.map((item: BusinessGoalCompletionSummary) => {
        return {
          ...item,
          Amount: Number(item.Amount),
          SelfRelianceCompletionRate: Number(item.SelfRelianceCompletionRate),
          SelfRelianceCount: Number(item.SelfRelianceCount),
          TargetAmount: Number(item.TargetAmount),
          TargetAmountCompletionRate: Number(item.TargetAmountCompletionRate),
          TargetSelfRelianceCount: Number(item.TargetSelfRelianceCount),
        };
      });
      goalsList.value = newData;
    } else {
      goalsList.value = [];
    }
  } catch (error) {
    goalsList.value = [];
  } finally {
    loading.value = false;
  }
};

const handleGoalDetails = (month: string, type: string) => {
  goalMonth.value = month;
  goalType.value = type;
  dialogVisible.value.goalsDetails = true;
};

const handleAddGoal = () => {
  isEdit.value = false;
  goalMonth.value = dayjs(new Date()).subtract(1, "month").format("YYYY-MM");
  dialogVisible.value.operationalGoals = true;
};

const getProgressColor = (percentage: number) => {
  return percentage >= currentDateProgress.value ? "#25B8A3" : "#FF5F5C";
};

const handleCarouselChange = (index: number) => {
  currentPage.value = index;
};

const handleAddGoalSubmit = () => {
  const params = addGoalsRefs.value?.handleSubmit();
  if (!params) {
    return;
  }
  console.log("params", params);
  const fun = isEdit.value ? Passport_Api.updateBusinessGoal : Passport_Api.insertBusinessGoal;
  dialogConfirmLoading.value = true;
  fun(params)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content);
        dialogVisible.value.operationalGoals = false;
        refreshGoalsList();
      } else {
        ElMessage.error(res.Content);
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleEditGoal = (goal: PageBusinessGoalCompletionSummary) => {
  goalMonth.value = goal.Month;
  isEdit.value = true;
  dialogVisible.value.operationalGoals = true;
};

onMounted(() => {
  getGoalsList();
});
</script>

<style scoped lang="scss">
.operational-goals {
  display: flex;
  height: 100%;
  padding: 20px;
  gap: 30px;
  background: var(--el-bg-color-overlay);
}

.left-section {
  width: 280px;
  flex-shrink: 0;
}

.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0;
}

.switch-controls {
  display: flex;
  gap: 15px;
  padding: 0 20px;

  :deep(.el-button) {
    padding: 8px 24px;
  }
}

.carousel-container {
  flex: 1;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;
  overflow: hidden;
  position: relative;

  :deep(.el-carousel),
  :deep(.el-carousel__container) {
    height: 100%;
  }

  :deep(.el-carousel__item) {
    padding: 24px;
    overflow-y: auto;
  }
}

.goals-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.goal-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  position: relative;
  width: 30%;

  .card-header {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #303133;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .edit-button {
      position: absolute;
      right: 0;
      top: 0;
      opacity: 0.7;
      transition: all 0.3s;

      &:hover {
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }

  .goal-item {
    margin-bottom: 15px;
    cursor: pointer;

    .goal-label {
      margin-bottom: 8px;
      color: #606266;
      font-weight: 500;
      font-size: 16px;
    }
  }
}

.add-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  height: 180px;
  border: 1px dashed var(--el-bg-color-overlay);
  transition: all 0.3s;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;
  height: 200px;
  width: 100%;

  &:hover {
    border-color: #409eff;
    color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}
</style>
