<template>
  <div class="goal-group-container">
    <div class="table-content">
      <el-table
        :data="tableDataList"
        style="width: 100%"
        row-key="Id"
        border
        :tree-props="{ children: 'Children', hasChildren: 'HasChildren' }"
        :header-cell-style="{ 'text-align': 'center' }"
        highlight-current-row
      >
        <el-table-column
          prop="Name"
          :label="handleGetLabel()"
          fixed="left"
          align="center"
          header-align="center"
        />
        <el-table-column
          v-if="goalType === 'amount'"
          prop="TargetAmount"
          label="目标金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: blue; font-weight: 700">¥{{ Number(scope.row.TargetAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="IncreaseAmount"
          label="新增金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div @click="handleLookDetail(scope.row, 'IncreaseAmount')">
              <span style="color: brown; font-weight: 700">
                ¥{{ Number(scope.row.IncreaseAmount).toFixed(2) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="月付费金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div @click="handleLookDetail(scope.row, 'MonthAmount')">
              <span>¥{{ Number(scope.row.MonthAmount).toFixed(2) }}</span>
              <span
                style="margin-left: 10px"
                :style="{
                  color: Number(scope.row.AmountGrowthRate) > 0 ? '#10b25c' : '#c20000',
                }"
              >
                ({{
                  (Number(scope.row.AmountGrowthRate || 0) > 0 ? "+" : "") +
                  Number(scope.row.AmountGrowthRate || 0).toFixed(2) +
                  "%"
                }})
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="月付费单数" align="center" header-align="center">
          <template #default="scope">
            <div @click="handleLookDetail(scope.row, 'PrescriptionCount')">
              <span>{{ Number(scope.row.PrescriptionCount).toFixed(0) }}</span>
              <span
                style="margin-left: 10px"
                :style="{
                  color: Number(scope.row.PrescriptionCountGrowthRate) > 0 ? '#10b25c' : '#c20000',
                }"
              >
                ({{
                  (Number(scope.row.PrescriptionCountGrowthRate || 0) > 0 ? "+" : "") +
                  Number(scope.row.PrescriptionCountGrowthRate || 0).toFixed(2) +
                  "%"
                }})
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="MonthTargetAmountCompletionRate"
          label="目标完成率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <LineProgress
              :percentage="
                scope.row.MonthTargetAmountCompletionRate > 100
                  ? 100
                  : scope.row.MonthTargetAmountCompletionRate * 1
              "
              :color="getProgressColor(scope.row.MonthTargetAmountCompletionRate || 0)"
              :format="
                (e: number) =>
                  `${Number(scope.row.MonthTargetAmountCompletionRate).toFixed(2) || 0}%`
              "
              :middle-progress="{
                percentage: currentDateProgress,
                color: '#FFC7BA',
              }"
              :text-style="{
                color: getProgressColor(scope.row.MonthTargetAmountCompletionRate),
                fontSize: '12px',
              }"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="客单价"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>¥{{ Number(scope.row.CustomerUnitPrice).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="ReturnVisitCount"
          label="引流回院数"
          align="center"
          header-align="center"
        />
        <el-table-column
          v-if="goalType === 'amount'"
          label="续方金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>¥{{ Number(scope.row.PrescriptionContinueAmount).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="续方单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>
              {{ Number(scope.row.PrescriptionContinueCount).toFixed(0) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="TargetSelfRelianceCount"
          label="自运行目标（单）"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: blue; font-weight: 700">
              {{ Number(scope.row.TargetSelfRelianceCount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="IncreaseSelfReliancePrescriptionCount"
          label="新增自运行单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span
              style="color: brown; font-weight: 700"
              @click="handleLookDetail(scope.row, 'IncreaseSelfReliancePrescriptionCount')"
            >
              {{ Number(scope.row.IncreaseSelfReliancePrescriptionCount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          label="月自运行单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div @click="handleLookDetail(scope.row, 'MonthSelfReliancePrescriptionCount')">
              <span>
                {{ Number(scope.row.MonthSelfReliancePrescriptionCount).toFixed(0) }}
              </span>
              <span
                style="margin-left: 10px"
                :style="{
                  color: Number(scope.row.SelfRelianceGrowthRate) > 0 ? '#10b25c' : '#c20000',
                }"
              >
                ({{
                  (Number(scope.row.SelfRelianceGrowthRate || 0) > 0 ? "+" : "") +
                  Number(scope.row.SelfRelianceGrowthRate || 0).toFixed(2) +
                  "%"
                }})
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="MonthTargetSelfRelianceCompletionRate"
          label="目标完成率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <LineProgress
              :percentage="
                scope.row.MonthTargetSelfRelianceCompletionRate > 100
                  ? 100
                  : scope.row.MonthTargetSelfRelianceCompletionRate * 1
              "
              :color="getProgressColor(scope.row.MonthTargetSelfRelianceCompletionRate || 0)"
              :middle-progress="{
                percentage: currentDateProgress,
                color: '#FFC7BA',
              }"
              :format="
                (e: number) =>
                  `${Number(scope.row.MonthTargetSelfRelianceCompletionRate).toFixed(2) || 0}%`
              "
              :text-style="{
                color: getProgressColor(scope.row.MonthTargetSelfRelianceCompletionRate),
                fontSize: '12px',
              }"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="SelfRunRate"
          label="自运行率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div>
              <div class="progress-wrapper">
                <span>上月</span>
                <el-progress
                  :percentage="
                    scope.row.PreviousSelfRelianceRate > 100
                      ? 100
                      : scope.row.PreviousSelfRelianceRate * 1
                  "
                  color="#F9C686"
                  :show-text="false"
                />
                <span class="percentage-text" :style="{ color: '#F9C686' }">
                  {{ Number(scope.row.PreviousSelfRelianceRate).toFixed(2) || 0 }}%
                </span>
              </div>
              <div class="progress-wrapper">
                <span>本月</span>
                <el-progress
                  :percentage="
                    scope.row.SelfRelianceRate > 100 ? 100 : scope.row.SelfRelianceRate * 1
                  "
                  color="#76CDF7"
                  :show-text="false"
                />
                <span class="percentage-text" :style="{ color: '#76CDF7' }">
                  {{ Number(scope.row.SelfRelianceRate).toFixed(2) || 0 }}%
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="total-table-wrapper">
      <el-table
        :data="totalDataList"
        style="width: 100%"
        row-key="Id"
        border
        :tree-props="{ children: 'Children', hasChildren: 'HasChildren' }"
        :header-cell-style="{ 'text-align': 'center' }"
        highlight-current-row
      >
        <el-table-column
          prop="Name"
          label="地区"
          fixed="left"
          align="center"
          header-align="center"
        />
        <el-table-column
          v-if="goalType === 'amount'"
          prop="TargetAmount"
          label="目标金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: blue; font-weight: 700">¥{{ Number(scope.row.TargetAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="IncreaseAmount"
          label="新增金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: brown; font-weight: 700">
              ¥{{ Number(scope.row.IncreaseAmount).toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="月付费金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>¥{{ Number(scope.row.MonthAmount).toFixed(2) }}</span>
            <span
              style="margin-left: 10px"
              :style="{
                color: Number(scope.row.AmountGrowthRate) > 0 ? '#10b25c' : '#c20000',
              }"
            >
              ({{
                (Number(scope.row.AmountGrowthRate || 0) > 0 ? "+" : "") +
                Number(scope.row.AmountGrowthRate || 0).toFixed(2) +
                "%"
              }})
            </span>
          </template>
        </el-table-column>
        <el-table-column label="月付费单数" align="center" header-align="center">
          <template #default="scope">
            <span>{{ Number(scope.row.PrescriptionCount).toFixed(0) }}</span>
            <span
              style="margin-left: 10px"
              :style="{
                color: Number(scope.row.PrescriptionCountGrowthRate) > 0 ? '#10b25c' : '#c20000',
              }"
            >
              ({{
                (Number(scope.row.PrescriptionCountGrowthRate || 0) > 0 ? "+" : "") +
                Number(scope.row.PrescriptionCountGrowthRate || 0).toFixed(2) +
                "%"
              }})
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="MonthTargetAmountCompletionRate"
          label="目标完成率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <LineProgress
              :percentage="
                scope.row.MonthTargetAmountCompletionRate > 100
                  ? 100
                  : scope.row.MonthTargetAmountCompletionRate * 1
              "
              :color="getProgressColor(scope.row.MonthTargetAmountCompletionRate || 0)"
              :middle-progress="{
                percentage: currentDateProgress,
                color: '#FFC7BA',
              }"
              :format="
                (e: number) =>
                  `${Number(scope.row.MonthTargetAmountCompletionRate).toFixed(2) || 0}%`
              "
              :text-style="{
                color: getProgressColor(scope.row.MonthTargetAmountCompletionRate),
                fontSize: '12px',
              }"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="客单价"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>¥{{ Number(scope.row.CustomerUnitPrice).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          prop="ReturnVisitCount"
          label="引流回院数"
          align="center"
          header-align="center"
        />
        <el-table-column
          v-if="goalType === 'amount'"
          label="续方金额"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>¥{{ Number(scope.row.PrescriptionContinueAmount).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'amount'"
          label="续方单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>
              {{ Number(scope.row.PrescriptionContinueCount).toFixed(0) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="TargetSelfRelianceCount"
          label="自运行目标（单）"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: blue; font-weight: 700">
              {{ Number(scope.row.TargetSelfRelianceCount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="IncreaseSelfReliancePrescriptionCount"
          label="新增自运行单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span style="color: brown; font-weight: 700">
              {{ Number(scope.row.IncreaseSelfReliancePrescriptionCount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          label="月自运行单数"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <span>
              {{ Number(scope.row.MonthSelfReliancePrescriptionCount).toFixed(0) }}
            </span>
            <span
              style="margin-left: 10px"
              :style="{
                color: Number(scope.row.SelfRelianceGrowthRate) > 0 ? '#10b25c' : '#c20000',
              }"
            >
              ({{
                (Number(scope.row.SelfRelianceGrowthRate || 0) > 0 ? "+" : "") +
                Number(scope.row.SelfRelianceGrowthRate || 0).toFixed(2) +
                "%"
              }})
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="MonthTargetSelfRelianceCompletionRate"
          label="目标完成率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <LineProgress
              :percentage="
                scope.row.MonthTargetSelfRelianceCompletionRate > 100
                  ? 100
                  : scope.row.MonthTargetSelfRelianceCompletionRate * 1
              "
              :color="getProgressColor(scope.row.MonthTargetSelfRelianceCompletionRate || 0)"
              :middle-progress="{
                percentage: currentDateProgress,
                color: '#FFC7BA',
              }"
              :format="
                (e: number) =>
                  `${Number(scope.row.MonthTargetSelfRelianceCompletionRate).toFixed(2) || 0}%`
              "
              :text-style="{
                color: getProgressColor(scope.row.MonthTargetSelfRelianceCompletionRate),
                fontSize: '12px',
              }"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="goalType === 'selfReliance'"
          prop="SelfRunRate"
          label="自运行率"
          align="center"
          header-align="center"
        >
          <template #default="scope">
            <div>
              <div class="progress-wrapper">
                <span>上月</span>
                <el-progress
                  :percentage="
                    scope.row.PreviousSelfRelianceRate > 100
                      ? 100
                      : scope.row.PreviousSelfRelianceRate * 1
                  "
                  color="#F9C686"
                  :show-text="false"
                />
                <span class="percentage-text" :style="{ color: '#F9C686' }">
                  {{ Number(scope.row.PreviousSelfRelianceRate).toFixed(2) || 0 }}%
                </span>
              </div>
              <div class="progress-wrapper">
                <span>本月</span>
                <el-progress
                  :percentage="
                    scope.row.SelfRelianceRate > 100 ? 100 : scope.row.SelfRelianceRate * 1
                  "
                  color="#76CDF7"
                  :show-text="false"
                />
                <span class="percentage-text" :style="{ color: '#76CDF7' }">
                  {{ Number(scope.row.SelfRelianceRate).toFixed(2) || 0 }}%
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BusinessGoalCompletionDetail } from "@/api/report/types";
import { getDecimalNum } from "@/utils";
import { handleGoalGroupJump } from "./goalGroupJump";
import dayjs from "dayjs";

import LineProgress from "./LineProgress.vue";

// 扩展接口以包含分组所需的属性
export interface BusinessGoalCompletionDetailWithChildren
  extends Partial<BusinessGoalCompletionDetail> {
  Id: string;
  Name: string;
  Children?: BusinessGoalCompletionDetailWithChildren[];
  HasChildren?: boolean;
  GroupName?: string;
  RegionName?: string;
}

const tableDataList = ref<BusinessGoalCompletionDetailWithChildren[]>([]);
const totalDataList = ref<BusinessGoalCompletionDetail[]>([]);

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  return percentage >= currentDateProgress.value ? "#25B8A3" : "#FF5F5C";
};

/** 获取当前日期进度 */
const getCurrentDateProgress = (): number => {
  try {
    // 获取当前时间
    const currentTime = dayjs();

    // 获取当月开始时间（月初00:00:00）
    const monthStart = dayjs().startOf("month");

    // 获取当月结束时间（月末23:59:59）
    const monthEnd = dayjs().endOf("month");

    // 计算当前时间距离月初的时间差（毫秒）
    const currentDiff = currentTime.valueOf() - monthStart.valueOf();

    // 计算当月总时间差（毫秒）
    const totalDiff = monthEnd.valueOf() - monthStart.valueOf();

    // 使用getDecimalNum进行精确的百分比计算，保留2位小数
    const progress = getDecimalNum(currentDiff).div(getDecimalNum(totalDiff)).times(100).toNumber();

    // 确保返回值在0-100范围内
    return Math.max(0, Math.min(100, Number(progress.toFixed(2))));
  } catch (error) {
    // 如果计算过程中出现错误，返回0
    console.error("计算当前日期进度时出错:", error);
    return 0;
  }
};
const currentDateProgress = ref<number>(getCurrentDateProgress());

const handleLookDetail = (row: BusinessGoalCompletionDetailWithChildren, type: string) => {
  const group =
    props.groupByMode === 0 ? "region" : props.groupByMode === 1 ? "assistant" : "personCharge";
  handleGoalGroupJump(row, type, group, props.dataRange, props.dayRange);
};

const handleTotalData = (list: BusinessGoalCompletionDetail[]) => {
  // 创建一个包含所有必要属性的对象
  const totalValues = calculateTotalValues(list);

  // 创建一个初始total对象，包含基本属性
  const total: Partial<BusinessGoalCompletionDetail> = {
    Id: "total",
    Name: "总计",
    ...totalValues,
  };

  // 计算所有比率并添加到total对象
  const rates = calculateRates(total as Record<string, unknown>);
  Object.assign(total, rates);

  // 确保total包含BusinessGoalCompletionDetail的所有必要属性后，再进行转换
  totalDataList.value = [total as BusinessGoalCompletionDetail];
};

// 接口定义用于计算比率方法的返回类型
interface CalculatedRates {
  AmountGrowthRate: string;
  PrescriptionCountGrowthRate: string;
  MonthTargetAmountCompletionRate: string;
  CustomerUnitPrice: string;
  SelfRelianceGrowthRate: string;
  MonthTargetSelfRelianceCompletionRate: string;
  PreviousSelfRelianceRate: string;
  SelfRelianceRate: string;
}

// 通用计算函数，用于计算比率
const calculateRate = (numerator: unknown, denominator: unknown, multiply = 100): string => {
  if (!denominator || denominator === "0") return "0";
  return getDecimalNum(Number(numerator))
    .div(getDecimalNum(Number(denominator)))
    .times(multiply)
    .toNumber()
    .toFixed(2);
};

// 通用增长率计算函数
const calculateGrowthRate = (current: unknown, previous: unknown): string => {
  if (!previous || previous === "0") return "0";
  return getDecimalNum(Number(current))
    .minus(getDecimalNum(Number(previous)))
    .div(getDecimalNum(Number(previous)))
    .times(100)
    .toNumber()
    .toFixed(2);
};

// 通用的比率计算方法
const calculateRates = (data: Record<string, unknown>): CalculatedRates => {
  return {
    AmountGrowthRate: calculateGrowthRate(data.MonthAmount, data.PreviousMonthAmount),
    PrescriptionCountGrowthRate: calculateGrowthRate(
      data.PrescriptionCount,
      data.PreviousPrescriptionCount
    ),
    MonthTargetAmountCompletionRate: calculateRate(data.MonthAmount, data.TargetAmount),
    CustomerUnitPrice: calculateRate(data.MonthAmount, data.PrescriptionCount, 1),
    SelfRelianceGrowthRate: calculateGrowthRate(
      data.MonthSelfReliancePrescriptionCount,
      data.PreviousMonthSelfReliancePrescriptionCount
    ),
    MonthTargetSelfRelianceCompletionRate: calculateRate(
      data.MonthSelfReliancePrescriptionCount,
      data.TargetSelfRelianceCount
    ),
    PreviousSelfRelianceRate: calculateRate(
      data.PreviousMonthSelfReliancePrescriptionCount,
      data.PreviousPrescriptionCount
    ),
    SelfRelianceRate: calculateRate(
      data.MonthSelfReliancePrescriptionCount,
      data.PrescriptionCount
    ),
  };
};

const handleGetLabel = (): string => {
  const map = ["地区", "医助", "管理负责人"];
  return map[props.groupByMode];
};

// 定义一个接口来表示calculateTotalValues的返回类型
interface TotalValues {
  IncreaseAmount: string;
  MonthAmount: string;
  PreviousMonthAmount: string;
  PrescriptionCount: string;
  PreviousPrescriptionCount: string;
  TargetAmount: string;
  ReturnVisitCount: string;
  IncreaseSelfReliancePrescriptionCount: string;
  MonthSelfReliancePrescriptionCount: string;
  PreviousMonthSelfReliancePrescriptionCount: string;
  TargetSelfRelianceCount: string;
  PrescriptionContinueAmount: string;
  PrescriptionContinueCount: string;
}

const calculateTotalValues = (items: BusinessGoalCompletionDetail[]): TotalValues => {
  const initialTotal = {
    IncreaseAmount: 0,
    MonthAmount: 0,
    PreviousMonthAmount: 0,
    PrescriptionCount: 0,
    PreviousPrescriptionCount: 0,
    TargetAmount: 0,
    ReturnVisitCount: 0,
    IncreaseSelfReliancePrescriptionCount: 0,
    MonthSelfReliancePrescriptionCount: 0,
    PreviousMonthSelfReliancePrescriptionCount: 0,
    TargetSelfRelianceCount: 0,
    PrescriptionContinueAmount: 0,
    PrescriptionContinueCount: 0,
  };

  // 计算数值总和
  items.forEach((item) => {
    Object.keys(initialTotal).forEach((key) => {
      const typedKey = key as keyof typeof initialTotal;
      initialTotal[typedKey] = getDecimalNum(initialTotal[typedKey])
        .plus(getDecimalNum(Number(item[typedKey as keyof BusinessGoalCompletionDetail] || 0)))
        .toNumber();
    });
  });

  // 将数值转换为字符串格式的结果
  const total = Object.entries(initialTotal).reduce((acc, [key, value]) => {
    acc[key as keyof TotalValues] = String(value);
    return acc;
  }, {} as TotalValues);

  return total;
};

const handleProcessingData = (list: BusinessGoalCompletionDetail[]) => {
  // 按 RegionName 分组并构造最终数据结构
  const result: BusinessGoalCompletionDetailWithChildren[] = list.reduce(
    (current: BusinessGoalCompletionDetailWithChildren[], item) => {
      const targetGroup = !item.GroupName
        ? getOrCreateNoRegionGroup(current)
        : getOrCreateRegionGroup(current, item);

      if (targetGroup) {
        const itemWithChildren = item as BusinessGoalCompletionDetailWithChildren;
        itemWithChildren.HasChildren = false;
        if (targetGroup.Children) {
          targetGroup.Children.push(itemWithChildren);
        }
      }
      return current;
    },
    []
  );

  // 计算每个区域的汇总数据
  result.forEach((region) => {
    if (region.Children) {
      Object.assign(
        region,
        calculateTotalValues(region.Children as BusinessGoalCompletionDetail[])
      );
      // 使用类型断言确保类型兼容性
      Object.assign(region, calculateRates(region as unknown as Record<string, unknown>));
    }
  });

  tableDataList.value = result;
};

const getOrCreateNoRegionGroup = (
  current: BusinessGoalCompletionDetailWithChildren[]
): BusinessGoalCompletionDetailWithChildren => {
  let noRegionGroup = current.find((r) => r.Name === "");
  if (!noRegionGroup) {
    noRegionGroup = {
      Id: "no-region",
      Name: "",
      Children: [],
    } as BusinessGoalCompletionDetailWithChildren;
    current.push(noRegionGroup);
  }
  return noRegionGroup;
};

const getOrCreateRegionGroup = (
  current: BusinessGoalCompletionDetailWithChildren[],
  item: BusinessGoalCompletionDetail
): BusinessGoalCompletionDetailWithChildren => {
  const itemWithGroup = item as unknown as BusinessGoalCompletionDetailWithChildren;
  let region = current.find((r) => r.Name === itemWithGroup.GroupName);
  if (!region) {
    region = {
      Id: current.length + 1 + "",
      Name: itemWithGroup.GroupName || "",
      RegionName: item.Name,
      Children: [],
    } as BusinessGoalCompletionDetailWithChildren;
    current.push(region);
  }
  return region;
};

interface Props {
  goalMonth: string;
  goalType: string;
  tableData: BusinessGoalCompletionDetail[];
  groupByMode: number;
  dataRange: string[];
  dayRange: string[];
}

const props = withDefaults(defineProps<Props>(), {
  goalType: "",
  goalMonth: "",
  tableData: () => [],
  groupByMode: 0,
});

watch(
  () => props.tableData,
  (newVal) => {
    if (newVal) {
      // 获取数据
      if (props.groupByMode === 0) {
        handleProcessingData(newVal);
      } else if (props.groupByMode === 1) {
        tableDataList.value = newVal as BusinessGoalCompletionDetailWithChildren[];
      } else if (props.groupByMode === 2) {
        handleProcessingData(newVal);
      }
      // 计算合计数据
      handleTotalData(newVal);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.goal-group-container {
  position: relative;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  width: 100%;
}

.total-table-wrapper {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 2;

  :deep(.el-table) {
    margin-bottom: 0;
  }
}

.table-content {
  flex: 1;
  overflow-y: auto;
  margin-bottom: -1px;

  :deep(.el-table) {
    border-bottom: none;
  }
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;

  :deep(.el-progress) {
    flex: 1;
    margin-right: 8px;
  }

  .percentage-text {
    font-size: 13px;
    min-width: 45px;
    text-align: right;
  }
}
</style>
