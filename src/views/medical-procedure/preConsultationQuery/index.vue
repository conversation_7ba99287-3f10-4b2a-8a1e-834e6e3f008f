<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="机构" prop="OrgIds">
                <HospitalSelect v-model="queryParams.OrgIds" multiple />
              </el-form-item>
              <el-form-item label="医生" prop="DctIds">
                <UserSelect
                  v-model="queryParams.DctIds"
                  :role-types="['doctor']"
                  :org-ids="queryParams.OrgIds"
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="患者名字/手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="UserName" label="姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="Age" label="年龄" align="center" />
          <el-table-column prop="UserPhone" label="手机号" align="center" />
          <el-table-column
            prop="CreatedTime"
            label="就诊时间"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column prop="OrgName" label="来源医院" align="center" />
          <el-table-column prop="DctName" label="来源医生" align="center" />
          <el-table-column prop="State" label="状态" align="center">
            <template #default="scope">1</template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150" align="center">
            <template #default="scope">
              <el-button
                v-if="scope.row.State === 0"
                link
                type="primary"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";

defineOptions({
  name: "PreConsultationQuery",
});

const queryParams = ref<any>({
  Source: null, //1为患者自己扫码  2为预问诊推荐
  Keyword: null, //患者名字，手机号
  DeptId: null,
  DctIds: null,
  StartDate: dayjs(new Date()).format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs(new Date()).format("YYYY-MM-DD 23:59:59"),
  PageIndex: 1,
  PageSize: 20,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseOrganization | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
};
const handleGetTableList = async () => {};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.StartDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
